﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Assets.Scripts.Backend.Commands;
using Assets.Scripts.DataHelpers;
using Assets.Scripts.DAO;
using System.IO;
using Assets.Scripts.CasualTools.Common.DB;
using Utils;

public class Tools
{
    [MenuItem("Assets/快速清档")]
    static void Clear()
    {
        if (EditorUtility.DisplayDialog("确认清档", "确定要清除所有本地存档数据吗？\n此操作不可撤销！", "确定", "取消"))
        {
            ClearAllSaveData();
        }
    }

    //[MenuItem("Assets/完全清档")]
    //static void CompleteWipe()
    //{
    //    if (EditorUtility.DisplayDialog("确认完全清档", "确定要完全清除所有数据吗？\n包括PlayerPrefs、数据库、缓存文件等\n此操作不可撤销！", "确定", "取消"))
    //    {
    //        CompleteWipeAllData();
    //    }
    //}

    //[MenuItem("Assets/简单清档")]
    //static void SimpleClear()
    //{
    //    if (EditorUtility.DisplayDialog("确认简单清档", "执行简单的清档操作\n只清除PlayerPrefs和数据库文件", "确定", "取消"))
    //    {
    //        SimpleWipeData();
    //    }
    //}

    /// <summary>
    /// 简单清档（只清除基本数据）
    /// </summary>
    //static void SimpleWipeData()
    //{
    //    Debug.Log("开始简单清档操作...");

    //    try
    //    {
    //        // 1. 清除PlayerPrefs
    //        PlayerPrefs.DeleteAll();
    //        PlayerPrefs.Save();
    //        Debug.Log("PlayerPrefs已清除");

    //        // 2. 尝试删除数据库文件
    //        try
    //        {
    //            string dbPath = DatabaseManager.FullDbPath;
    //            Debug.Log("数据库路径: " + dbPath);

    //            if (File.Exists(dbPath))
    //            {
    //                File.Delete(dbPath);
    //                Debug.Log("数据库文件已删除");
    //            }
    //            else
    //            {
    //                Debug.Log("数据库文件不存在");
    //            }
    //        }
    //        catch (System.Exception e)
    //        {
    //            Debug.LogWarning("删除数据库文件失败: " + e.Message);
    //        }

    //        Debug.Log("✅ 简单清档操作完成！");
    //        EditorUtility.DisplayDialog("简单清档完成", "基本存档数据已清除！", "确定");
    //    }
    //    catch (System.Exception e)
    //    {
    //        Debug.LogError("简单清档操作出错: " + e.Message);
    //        EditorUtility.DisplayDialog("简单清档失败", "简单清档操作出现错误:\n" + e.Message, "确定");
    //    }
    //}

    /// <summary>
    /// 清除所有存档数据（推荐使用）
    /// </summary>
    static void ClearAllSaveData()
    {
        Debug.Log("开始清档操作...");

        try
        {
            // 1. 先使用ResetManager进行内存数据重置（但跳过云存储）
            Debug.Log("执行内存数据重置...");
            ResetManagerSafe();

            // 2. 关闭数据库连接
            CloseDatabaseConnections();

            // 3. 清除PlayerPrefs
            PlayerPrefs.DeleteAll();
            PlayerPrefs.Save();
            Debug.Log("PlayerPrefs已清除");

            // 4. 删除数据库文件
            DeleteDatabaseFiles();

            // 5. 清除persistentDataPath下的存档文件
            ClearPersistentDataFiles();

            // 6. 清除特定的PlayerPrefs键
            ClearSpecificPlayerPrefsKeys();

            Debug.Log("✅ 清档操作完成！");
            EditorUtility.DisplayDialog("清档完成", "所有存档数据已清除！", "确定");
        }
        catch (System.Exception e)
        {
            Debug.LogError("清档操作出错: " + e.Message);
            EditorUtility.DisplayDialog("清档失败", "清档操作出现错误:\n" + e.Message, "确定");
        }
    }

    /// <summary>
    /// 安全的ResetManager调用（跳过可能有问题的部分）
    /// </summary>
    static void ResetManagerSafe()
    {
        try
        {
            // 手动执行Reset操作，避免调用可能有问题的WipeAll
            Debug.Log("执行FastPropertiesHelper.Reset()");
            if (FastPropertiesHelper.Instance != null)
                FastPropertiesHelper.Instance.Reset();

            Debug.Log("执行InventoryHelper.Reset()");
            if (InventoryHelper.Instance != null)
                InventoryHelper.Instance.Reset();

            Debug.Log("执行LevelHelper.Reset()");
            if (LevelHelper.Instance != null)
                LevelHelper.Instance.Reset();

            Debug.Log("执行UserSettings.Reset()");
            if (UserSettings.Instance != null)
                UserSettings.Instance.Reset();

            Debug.Log("执行UserIdHelper.Reset()");
            if (UserIdHelper.Instance != null)
                UserIdHelper.Instance.Reset();

            Debug.Log("内存数据重置完成");
        }
        catch (System.Exception e)
        {
            Debug.LogWarning("ResetManager部分操作失败: " + e.Message);
        }
    }

    /// <summary>
    /// 关闭数据库连接
    /// </summary>
    static void CloseDatabaseConnections()
    {
        try
        {
            Debug.Log("关闭数据库连接...");

            // 尝试关闭DatabaseManager实例
            if (DatabaseManager.Instance != null)
            {
                DatabaseManager.Instance.Close();
                Debug.Log("DatabaseManager连接已关闭");
            }

            // 强制垃圾回收，释放可能的文件句柄
            System.GC.Collect();
            System.GC.WaitForPendingFinalizers();
            System.GC.Collect();

            Debug.Log("数据库连接关闭完成");
        }
        catch (System.Exception e)
        {
            Debug.LogWarning("关闭数据库连接时出错: " + e.Message);
        }
    }

    /// <summary>
    /// 删除数据库文件
    /// </summary>
    static void DeleteDatabaseFiles()
    {
        try
        {
            string dbPath = DatabaseManager.FullDbPath;
            Debug.Log("数据库路径: " + dbPath);

            if (File.Exists(dbPath))
            {
                // 尝试多次删除，处理文件锁定问题
                int attempts = 0;
                bool deleted = false;

                while (attempts < 3 && !deleted)
                {
                    try
                    {
                        File.Delete(dbPath);
                        deleted = true;
                        Debug.Log("数据库文件已删除");
                    }
                    catch (System.Exception e)
                    {
                        attempts++;
                        Debug.LogWarning($"删除数据库文件失败 (尝试 {attempts}/3): " + e.Message);

                        if (attempts < 3)
                        {
                            // 等待一下再重试
                            System.Threading.Thread.Sleep(100);
                            System.GC.Collect();
                            System.GC.WaitForPendingFinalizers();
                        }
                    }
                }

                if (!deleted)
                {
                    Debug.LogError("无法删除数据库文件，可能被其他进程占用");
                }
            }
            else
            {
                Debug.Log("数据库文件不存在");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError("删除数据库文件时出错: " + e.Message);
        }
    }

    /// <summary>
    /// 清除特定的PlayerPrefs键
    /// </summary>
    static void ClearSpecificPlayerPrefsKeys()
    {
        try
        {
            Debug.Log("清除特定PlayerPrefs键...");

            // 调用PlayerPrefsKeys.RemoveAll()
            PlayerPrefsKeys.RemoveAll();
            Debug.Log("PlayerPrefsKeys.RemoveAll() 已执行");

            // 额外清除一些可能遗漏的键
            string[] additionalKeys = {
                "TestKey1", "TestKey2", "TestKey3",
                "I2SourceVersion_", "LastGoogleUpdate_",
                "ConsentInitial", "ConsentStatus", "ConsentUsers", "ConsentTimestamp"
            };

            foreach (string key in additionalKeys)
            {
                if (PlayerPrefs.HasKey(key))
                {
                    PlayerPrefs.DeleteKey(key);
                    Debug.Log("已删除PlayerPrefs键: " + key);
                }
            }

            PlayerPrefs.Save();
            Debug.Log("特定PlayerPrefs键清除完成");
        }
        catch (System.Exception e)
        {
            Debug.LogWarning("清除特定PlayerPrefs键时出错: " + e.Message);
        }
    }

    /// <summary>
    /// 完全清除所有数据（包括缓存等）
    /// </summary>
    //static void CompleteWipeAllData()
    //{
    //    Debug.Log("开始完全清档操作...");

    //    try
    //    {
    //        // 先执行基本清档
    //        ClearAllSaveData();

    //        // 清除额外的缓存和临时文件
    //        ClearCacheAndTempFiles();

    //        Debug.Log("✅ 完全清档操作完成！");
    //        EditorUtility.DisplayDialog("完全清档完成", "所有数据（包括缓存）已清除！", "确定");
    //    }
    //    catch (System.Exception e)
    //    {
    //        Debug.LogError("完全清档操作出错: " + e.Message);
    //        EditorUtility.DisplayDialog("完全清档失败", "完全清档操作出现错误:\n" + e.Message, "确定");
    //    }
    //}

    /// <summary>
    /// 清除persistentDataPath下的存档文件
    /// </summary>
    static void ClearPersistentDataFiles()
    {
        string persistentPath = Application.persistentDataPath;
        Debug.Log("清理persistentDataPath: " + persistentPath);

        if (Directory.Exists(persistentPath))
        {
            // 需要清理的文件类型
            string[] filesToDelete = {
                "*.dat",    // InboxDAO等存档文件
                "*.loc",    // I2本地化文件
                "*.db",     // 数据库文件
                "*.sqlite", // SQLite文件
                "*.json",   // JSON配置文件
                "*.xml"     // XML配置文件
            };

            foreach (string pattern in filesToDelete)
            {
                string[] files = Directory.GetFiles(persistentPath, pattern, SearchOption.AllDirectories);
                foreach (string file in files)
                {
                    try
                    {
                        File.Delete(file);
                        Debug.Log("已删除文件: " + file);
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogWarning("无法删除文件 " + file + ": " + e.Message);
                    }
                }
            }

            // 清理特定目录
            string[] dirsToDelete = {
                "CSDownloads",      // 下载缓存
                "temp_audio",       // 音频缓存
                "onlinelevels",     // 在线关卡
                "Cache",            // 通用缓存
                "Logs"              // 日志文件
            };

            foreach (string dirName in dirsToDelete)
            {
                string dirPath = Path.Combine(persistentPath, dirName);
                if (Directory.Exists(dirPath))
                {
                    try
                    {
                        Directory.Delete(dirPath, true);
                        Debug.Log("已删除目录: " + dirPath);
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogWarning("无法删除目录 " + dirPath + ": " + e.Message);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 清除缓存和临时文件
    /// </summary>
    //static void ClearCacheAndTempFiles()
    //{
    //    Debug.Log("清理缓存和临时文件...");

    //    // 清理HTTP缓存
    //    try
    //    {
    //        string cacheFolder = Application.persistentDataPath;
    //        string[] cacheFiles = Directory.GetFiles(cacheFolder, "*cache*", SearchOption.AllDirectories);
    //        foreach (string file in cacheFiles)
    //        {
    //            try
    //            {
    //                File.Delete(file);
    //                Debug.Log("已删除缓存文件: " + file);
    //            }
    //            catch (System.Exception e)
    //            {
    //                Debug.LogWarning("无法删除缓存文件 " + file + ": " + e.Message);
    //            }
    //        }
    //    }
    //    catch (System.Exception e)
    //    {
    //        Debug.LogWarning("清理缓存文件时出错: " + e.Message);
    //    }

    //    // 清理Cookie
    //    try
    //    {
    //        string cookieFolder = Path.Combine(Application.persistentDataPath, "Cookies");
    //        if (Directory.Exists(cookieFolder))
    //        {
    //            Directory.Delete(cookieFolder, true);
    //            Debug.Log("已删除Cookie目录");
    //        }
    //    }
    //    catch (System.Exception e)
    //    {
    //        Debug.LogWarning("清理Cookie时出错: " + e.Message);
    //    }
    //}
}

// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.DataHelpers.InventoryHelper
using Assets.Scripts.Backend;
using Assets.Scripts.Backend.Commands;
using Assets.Scripts.CasualTools.Common.Logging;
using Assets.Scripts.CasualTools.Common.Tasks;
using Assets.Scripts.DAO;
using Assets.Scripts.DAO.Entity;
using Assets.Scripts.Logging;
using caravan.protobuf.messages;
using Facebook;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Utils;

namespace Assets.Scripts.DataHelpers
{
    public class InventoryHelper : IReset
    {
        private InventoryHelper()
        {
            this._dao = InventoryDao.Instance;
            this.CachedCoin = this._dao.GetInventoryItemCount(InventoryItemType.Coins);
            //this.NewCoin = _dao.GetInventoryItemCount(InventoryItemType.NewCoins);
        }

        public static InventoryHelper Instance
        {
            get
            {
                if (InventoryHelper._instance == null)
                {
                    InventoryHelper._instance = new InventoryHelper();
                }
                return InventoryHelper._instance;
            }
        }

        public int CachedCoin { get; private set; }

        public int DelayedCoins { get; set; }

        //public int NewCoin { get; set; }

        public bool IsUserTypeExists(UserType type)
        {
            byte b = (byte)InventoryHelper.Instance.GetItemAmount(InventoryItemType.UserType);
            byte b2 = (byte)(b & (byte)type);
            return b2 == (byte)type;
        }

        public void SetUserType(UserType type, bool nonSynced = true)
        {
            byte b = (byte)InventoryHelper.Instance.GetItemAmount(InventoryItemType.UserType);
            byte amount = (byte)(b | (byte)type);
            this.SetItemAmount(InventoryItemType.UserType, (int)amount, nonSynced, true);
        }

        public void Reset()
        {
            this._dao.RemoveAll();
            InventoryHelper._instance = null;
        }

        public bool AreThereUnsyncedItems()
        {
            return this._dao.AreThereUnsyncedItems();
        }

        public void MarkInventoriesAsSynced()
        {
            this._dao.MarkInventoriesAsSynced();
        }

        public void SyncInventoryFromBackend(InventoryMetaData inventory, bool markInventoriesSynced)
        {
            this.SetItemAmount(InventoryItemType.Coins, inventory.coins, true, false);
            this.CachedCoin = inventory.coins;
            this.DelayedCoins = 0;
            this.SetItemAmount(InventoryItemType.Anvil, inventory.anvil, true, false);
            this.SetItemAmount(InventoryItemType.Bomb, inventory.bomb, true, false);
            this.SetItemAmount(InventoryItemType.BoxingGlove, inventory.boxingGlove, true, false);
            this.SetItemAmount(InventoryItemType.DiscoBall, inventory.discoBall, true, false);
            this.SetItemAmount(InventoryItemType.Dice, inventory.shuffle, true, false);
            this.SetItemAmount(InventoryItemType.Extra5Moves, inventory.extra5Moves, true, false);
            this.SetItemAmount(InventoryItemType.Hammer, inventory.hammer, true, false);
            this.SetItemAmount(InventoryItemType.Rocket, inventory.rocket, true, false);
            this.SetItemAmount(InventoryItemType.UserType, inventory.userType, true, false);
            this.SetItemAmount(InventoryItemType.Stars, inventory.stars, true, false);
            this.SetItemAmount(InventoryItemType.UnlimitedLife, inventory.unlimitedLife, true, false);
            if (inventory.life < 0)
            {
                inventory.life = 0;
            }
            else if (inventory.life > DefaultUserLife)
            {
                inventory.life = DefaultUserLife;
            }
            LifeStatusHelper.Instance.UpdateLifeAndTimeFromBackend(inventory.life, inventory.lifeTime);
            UserIdHelper.Instance.IsPaidUser = this.IsUserTypeExists(UserType.USER_TYPE_PAID);
            if (UserIdHelper.Instance.IsPaidUser)
            {
                OneSignal.SendTag("PayerTag", "1");
            }
            if (markInventoriesSynced)
            {
                InventoryHelper.DeltaInventorySyncTimeStamp = -5f;
                this.MarkInventoriesAsSynced();
            }
        }

        public bool IsSynced(InventoryItemType type)
        {
            return this._dao.GetInventoryItemEntity(type).Synced == 1;
        }

        public int AddItemAmount(InventoryItemType type, int amount, bool syncNow = true)
        {
            //UnityEngine.Debug.Log("AddItemAmount" + type);
            //xiaoming �����Ʒ����
            if (amount == 0)
            {
                return this.GetItemAmount(type);
            }
            if (amount < 0)
            {
                LogManager.Error(LogTags.InventoryHelper, "A negative value can not be added to balance for type {0}", new object[]
                {
                    type
                });
                return this.GetItemAmount(type);
            }
            InventoryItemEntity inventoryItemEntity = this._dao.GetInventoryItemEntity(type);
            inventoryItemEntity.Amount += amount;
            inventoryItemEntity.Synced = 0;
            this._dao.CreateOrUpdate(inventoryItemEntity);
            LogManager.Debug(LogTags.InventoryHelper, "{0} added and new balance is {1} for {2}", new object[]
            {
                amount,
                inventoryItemEntity.Amount,
                type
            });
            if (syncNow)
            {
                this.InventoryChanged();
            }
            if (type == InventoryItemType.Coins)
            {
                this.CachedCoin = inventoryItemEntity.Amount;
            }
            return inventoryItemEntity.Amount;
        }

        public void SetItemAmount(InventoryItemType type, int amount, bool notSynced = false, bool syncNow = true)
        {
            if (amount < 0)
            {
                LogManager.Error(LogTags.InventoryHelper, "A negative value can not be set for type:", new object[]
                {
                    type
                });
            }
            InventoryItemEntity inventoryItemEntity = this._dao.GetInventoryItemEntity(type);
            inventoryItemEntity.Amount = amount;
            if (notSynced)
            {
                inventoryItemEntity.Synced = 0;
            }
            this._dao.CreateOrUpdate(inventoryItemEntity);
            if (syncNow)
            {
                this.InventoryChanged();
            }
            if (type == InventoryItemType.Coins)
            {
                this.CachedCoin = inventoryItemEntity.Amount;
            }
            LogManager.Debug(LogTags.InventoryHelper, "Forced balance is {0} for {1}", new object[]
            {
                amount,
                type
            });
        }

        public int GetItemAmount(InventoryItemType type)
        {
            return this._dao.GetInventoryItemCount(type);
        }

        public bool SpendItemIfPossible(InventoryItemType type, int amount, bool syncNow = true)
        {
            InventoryItemEntity inventoryItemEntity = this._dao.GetInventoryItemEntity(type);
            if (amount > inventoryItemEntity.Amount)
            {
                LogManager.Debug(LogTags.InventoryHelper, "Not enough balance ({0}) for value {1} of type {2}", new object[]
                {
                    inventoryItemEntity.Amount,
                    amount,
                    type
                });
                return false;
            }
            inventoryItemEntity.Amount -= amount;
            inventoryItemEntity.Synced = 0;
            this._dao.CreateOrUpdate(inventoryItemEntity);
            if (syncNow)
            {
                this.InventoryChanged();
            }
            if (type == InventoryItemType.Coins)
            {
                this.CachedCoin = inventoryItemEntity.Amount;
                //FacebookEvents.SendSpentCredits(amount);
            }
            LogManager.Debug(LogTags.InventoryHelper, "{0} decreased and new balance is {1} for type {2}", new object[]
            {
                amount,
                inventoryItemEntity.Amount,
                type
            });
            return true;
        }

        public void SetInitialValues()
        {
            if (!this._dao.HasInventoryItemCount(InventoryItemType.Coins))
            {
                this.SetItemAmount(InventoryItemType.Coins, DefaultUserCoins, true, true);
            }
            if (!this._dao.HasInventoryItemCount(InventoryItemType.Life))
            {
                this.SetItemAmount(InventoryItemType.Life, DefaultUserLife, true, true);
            }
        }

        public void RemovePiggyBankCoins()
        {
            if (this._dao.HasInventoryItemCount(InventoryItemType.PiggyBank))
            {
                this._dao.RemoveRowWithItemId(InventoryItemType.PiggyBank);
            }
        }

        private void InventoryChanged()
        {
            if (this._asynInventoryUpdate == null)
            {
                this._asynInventoryUpdate = new Task(this.SyncInventory(), true, LifeTime.App);
                return;
            }
            if (this._lastLogFrameCount == Time.frameCount)
            {
                return;
            }
            LogManager.Debug(LogTags.InventoryHelper, "AsynInventroyUpdate is not null.", new object[0]);
            this._lastLogFrameCount = Time.frameCount;
        }

        private void SyncReplied()
        {
            this._asynInventoryUpdate = null;
        }

        private void ForceSyncReplied()
        {
            InventoryHelper.DeltaInventorySyncTimeStamp = -5f;
            this.SyncReplied();
        }

        private IEnumerator SyncInventory()
        {
            yield return null;
            SyncInventoryCommand inventorySyncCommand = new SyncInventoryCommand(new Action(this.SyncReplied));
            List<Command> commandList = new List<Command>
            {
                inventorySyncCommand
            };
            bool wait = LifeHackHelper.WaitHackCheck;
            if (inventorySyncCommand.ShouldPerformed() && !wait)
            {
                BackendCaller.Instance.Send(commandList);
            }
            else
            {
                LogManager.Debug(LogTags.InventoryHelper, "Sync will be ignored since not needed ({0}).", new object[]
                {
                    wait
                });
                this.SyncReplied();
            }
            yield break;
        }

        public void ForceSyncServerNow()
        {
            LogManager.Debug(LogTags.InventoryHelper, "Force Sync Server Now!", new object[0]);
            if (this._asynInventoryUpdate != null && this._asynInventoryUpdate.Running)
            {
                this._asynInventoryUpdate.Stop();
            }
            this._asynInventoryUpdate = null;
            SyncInventoryCommand item = new SyncInventoryCommand(new Action(this.ForceSyncReplied));
            List<Command> commands = new List<Command>
            {
                item
            };
            BackendCaller.Instance.Send(commands);
        }

        public const int MaxDeltaInventorySyncTime = 5;

        public static float DeltaInventorySyncTimeStamp = -5f;

        private const int DefaultUserCoins = 200;

        private const int DefaultUserLife = 10;

        private static InventoryHelper _instance;

        private readonly InventoryDao _dao;

        private Task _asynInventoryUpdate;

        private int _lastLogFrameCount;
    }
}
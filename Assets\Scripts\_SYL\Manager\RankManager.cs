using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using URankModule;
using System;
using Assets.Scripts.CasualTools.SimpleProperties.DAO;
using Assets.Scripts.DataHelpers;
using Utils;


public class RankManager : MonoBehaviour
{
    public static RankManager Instance;
    public const string RankName = "level";
    
    public event Action OnRankListChanges;
    
    public PlayerData _myPlayerData;

    public List<PlayerData> _rankPlayerDatas;

    //private PlayerData myPlayerData;

    private void Awake()
    {
        Instance = this;
        _myPlayerData = new PlayerData();
        _rankPlayerDatas = new List<PlayerData>();
    }

    private void Start()
    {
#if UNITY_EDITOR
        UServerTestConfig config = new UServerTestConfig();
        config.appId = 160125;
        config.channelId = "9000000";
        config.uid = "123456789";
        config.version = "8";
        // 设置游戏的具体参数
        UCommonModule.TestHelper.setTestConfig(config);
#endif
        //UTestHelper.setTestConfig()

        //GetRankList();
        ReadMyRankData();
        GetRankList();
    }

    public Dictionary<string, object> SetPlayerData(string _uid)
    {
        Dictionary<string, object> u = new Dictionary<string, object>();

        u.Add("uid", _uid);
        u.Add("nickname", USDKUtils.UUser.NickName);
        u.Add("level", USDKDataSupport.Instance.PassedGameLevel);
        Dictionary<string, object> gamedata = new Dictionary<string, object>();
        gamedata.Add("user", u);
        return gamedata;
    }

    //上传玩家数据
    public void SendPlayerData()
    {
        if (!USDKUtils.UCommon.IsHaveInternet)
            return;
        if (string.IsNullOrEmpty(USDKUtils.UUser.UID))
            return;
        string str = MiniJSON.Json.Serialize(SetPlayerData(string.IsNullOrEmpty(USDKUtils.UUser.UID) ? "123456789" : USDKUtils.UUser.UID));
        Debug.Log("上传玩家列数据str" + ":" + str);

#if UNITY_IOS

        RankRequestUrl.Instance.SendPlayerData(str, USDKUtils.UUser.UID, SendPlayerDataBack);
#else

        //CommonPVPRank.Instance.SetPvpArchive(str, SendPlayerDataBack);
#endif
        _myPlayerData._nickName = USDKUtils.UUser.NickName;
    }

    private void SendPlayerDataBack(string str)
    {
        Debug.Log("上传玩家列数据" + ":" + str);
    }

    public void ReadMyRankData()
    {
        _myPlayerData._uid = USDKUtils.UUser.UID;
        _myPlayerData._nickName = USDKUtils.UUser.NickName;
        _myPlayerData._rankI = UPlayerUtils.GetInt(RankName + "_RankI", 0);
        _myPlayerData._levelI = UPlayerUtils.GetInt(RankName + "_Level", 1);
        //if (_myPlayerData._uid == string.Empty)
        //{
        //    _myPlayerData._uid = USDKUtils.UUser.UID;
        //    _myPlayerData._nickName = USDKUtils.UUser.NickName;
        //    _myPlayerData._rankI = UPlayerUtils.GetInt(RankName + "_RankI", 0);
        //    _myPlayerData._levelI = UPlayerUtils.GetInt(RankName + "_Level", 1);
        //}
        //else if (_myPlayerData._uid == USDKUtils.UUser.UID)
        //{
        //    _myPlayerData._uid = USDKUtils.UUser.UID;
        //    _myPlayerData._nickName = USDKUtils.UUser.NickName;
        //    string userName = SimpleDbPropertyDao.Instance.FindValueByKey("user_name");
        //    if (userName == null)
        //    {
        //        userName = USDKUtils.UUser.NickName;
        //        _myPlayerData._nickName = USDKUtils.UUser.NickName;
        //        SimpleDbPropertyDao.Instance.UpdateOrCreateValueByKey("user_name", userName);
        //    }
        //    else if (_myPlayerData._nickName != userName)
        //    {
        //        _myPlayerData._nickName = userName;
        //    }
        //    _myPlayerData._rankI = UPlayerUtils.GetInt(RankName + "_RankI", 0);
        //    _myPlayerData._levelI = UPlayerUtils.GetInt(RankName + "_Level", 1);
        //}
    }

    private void SaveMyRankData()
    {
        UPlayerUtils.Save(RankName + "_RankI", _myPlayerData._rankI);
        UPlayerUtils.Save(RankName + "_Level", _myPlayerData._levelI);
    }

    /// <summary>
    /// 上传排行榜
    /// </summary>
    /// <param name="value">变化值</param>
    public void SendRankData(int value)
    {
        if (Application.isEditor && InitGameSet.Instance.beChangeRankData)
        {
        }
        Debug.Log(RankName + "排行榜上传： " + value);
#if UNITY_IOS
        {
            if (_myPlayerData._levelI >= value)
            {
                return;
            }
            value = value - _myPlayerData._levelI;
            RankRequestUrl.Instance.SetRanks(RankName, value, USDKUtils.UUser.UID, "", SendRankDataBack);
        }
#else
        {
            CommonRank.Instance.SetRanks(RankName, value, "", SendRankDataBack1);
            //CommonPVPRank.Instance.SetPvpRank(RankName, value, SendRankDataBack);
        }
#endif
    }
    private void SendRankDataBack1(float data1, float data2 ,string str)
    {
        Debug.Log("上传玩家排行榜数据" + ":" + str);
        //GetRankList();
    }
    private void SendRankDataBack(string str)
    {
        Debug.Log("上传玩家排行榜数据" + ":" + str);
        //GetRankList();
    }

    //获取排行榜
    public void GetRankList()
    {
#if UNITY_IOS
        {
            RankRequestUrl.Instance.GetRankList(USDKUtils.UUser.UID, CallBackRankDatas);
        }
#else
        {
            CommonRank.Instance.GetRanks(RankName, 100, "", CallBackRankDatas1);
            //CommonPVPRank.Instance.GetPvpRank(RankName, 1, 100, false, CallBackRankDatas);
        }
#endif
    }
    //排行榜数据解析
    public void CallBackRankDatas1(CommonRankData _datas)
    {
        if(_datas == null)
        {
            Debug.Log("排行榜数据：为空 ");
            return;
        }
        Debug.Log("排行榜数据： " + _datas.data);
        if (string.IsNullOrEmpty(_datas.data))
        {
            return;
        }
        if (_datas.myRank != null)
        {
            _myPlayerData._rankI = _datas.myRank.rankNum;
            _myPlayerData._levelI = (int)_datas.myRank.score;
            SaveMyRankData();
        }

        _rankPlayerDatas.Clear();
        for (int i = 0; i < _datas.ranks.Count; i++)
        {
            PlayerData _playerData = new PlayerData();

            _playerData._uid = _datas.ranks[i].uid;
            _playerData._nickName = _datas.ranks[i].nickname;
            _playerData._rankI = _datas.ranks[i].rankNum;
            _playerData._levelI = (int)_datas.ranks[i].score;
            _rankPlayerDatas.Add(_playerData);
        }



        //_rankPlayerDatas = DealArrayData(_datas.data);

        if (OnRankListChanges != null)
        {
            OnRankListChanges();
        }
    }
    //排行榜数据解析
    public void CallBackRankDatas(string datas)
    {
        Debug.Log("排行榜数据： " + datas);
        if (string.IsNullOrEmpty(datas))
        {
            return;
        }

        _rankPlayerDatas = DealArrayData(datas);

        if (OnRankListChanges != null)
        {
            OnRankListChanges();
        }
    }

    /// <summary>
    /// 解析玩家列表
    /// </summary>
    /// <param name="backString"></param>
    /// <returns></returns>
    public List<PlayerData> DealArrayData(string backString, bool setRank = true)
    {
        if (string.IsNullOrEmpty(backString))
        {
            return new List<PlayerData>();
        }
        // Debug.LogError(backString);
        Dictionary<string, object> BackJson = MiniJSON.Json.Deserialize(backString.Replace(@"\\", "")) as Dictionary<string, object>;

        if (setRank)
        {
            ///根据返回值设置本地存档
            if (BackJson.ContainsKey("myRank"))
            {
                int myRank = int.Parse(BackJson["myRank"].ToString());
                long myValue = BackJson.ContainsKey("myValue") ? long.Parse(BackJson["myValue"].ToString()) : 0;
                if (myValue >= int.MaxValue)
                    myValue = int.MaxValue - 1;

                _myPlayerData._rankI = myRank;
                _myPlayerData._levelI = (int)myValue;
                SaveMyRankData();
            }
        }

        if (!BackJson.ContainsKey("data"))
            return new List<PlayerData>();

        List<object> data = (BackJson["data"]) as List<object>;

        if (data == null)
        {
            Dictionary<string, object> tmp = BackJson["data"] as Dictionary<string, object>;
            if (tmp.ContainsKey("data"))
                data = tmp["data"] as List<object>;
        }

        List<PlayerData> gamedata = new List<PlayerData>();
        PlayerData gd = new PlayerData();

        Dictionary<string, object> jd = new Dictionary<string, object>();

        for (int i = 0; i < data.Count; i++)
        {
            jd = data[i] as Dictionary<string, object>;
            gd = new PlayerData();
            ///
            gd._uid = jd["uid"].ToString();
            if (string.IsNullOrEmpty(gd._uid))
            {
                continue;
            }
            //Debug.LogError(jd["uid"].ToString());
            gd._nickName = jd["nickname"].ToString();
            if (string.IsNullOrEmpty(gd._nickName))
            {
                gd._nickName = gd._uid;
            }
            //UnityEngine.Debug.Log(gd._nickName + " change name before " + USDKUtils.UUser.NickName);
            //string userName = SimpleDbPropertyDao.Instance.FindValueByKey("user_name");
            //if (userName == null)
            //{
            //    userName = USDKUtils.UUser.NickName;
            //    SimpleDbPropertyDao.Instance.UpdateOrCreateValueByKey("user_name", userName);
            //}
            //if (gd._uid == USDKUtils.UUser.UID && gd._nickName != userName)
            //{
            //    UnityEngine.Debug.Log(gd._nickName + " change name " + USDKUtils.UUser.NickName);
            //    gd._nickName = userName;
            //}
            //gd.user.icon = jd["icon"].ToString();

            gd._rankI = int.Parse(jd["rank"].ToString());
            gd._levelI = int.Parse(jd["score"].ToString());
            gamedata.Add(gd);


            //当本地昵称与排行榜昵称不一样时，重新上传玩家数据
            if (gd._uid.Equals(USDKUtils.UUser.UID))
            {
                gd._nickName = USDKUtils.UUser.NickName;
                if (!gd._nickName.Equals(USDKUtils.UUser.NickName))
                {
                    SendPlayerData();
                }
            }
        }
        return gamedata;
    }

}

public class PlayerData
{
    public string _uid = string.Empty;
    public string _nickName;
    public int _rankI;
    public int _levelI;
}
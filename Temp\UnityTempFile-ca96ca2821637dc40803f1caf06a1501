/target:library
/out:Temp/UnityEngine.XR.LegacyInputHelpers.dll
/nowarn:0169
/nowarn:0649
/debug:portable
/optimize-
/nostdlib+
/preferreduilang:en-US
/langversion:latest
/reference:Library/ScriptAssemblies/UnityEngine.SpatialTracking.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEditor.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/UnityExtensions/Unity/UnityVR/Editor/UnityEditor.VR.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
/reference:Assets/Dll/GMGSDK.dll
/reference:Assets/Dll/Howin.dll
/reference:Assets/Dll/IOSSDK.dll
/reference:Assets/Dll/LAdModule.dll
/reference:Assets/Dll/LAnalytics.dll
/reference:Assets/Dll/LauguageKeys.dll
/reference:Assets/Dll/LitJson.dll
/reference:Assets/Dll/LSDKInterface.dll
/reference:Assets/Dll/UCommonModule.dll
/reference:Assets/Dll/UI.dll
/reference:Assets/Dll/UnityEditor.UiOS.Extensions.Xcode.dll
/reference:Assets/Dll/URankModule.dll
/reference:Assets/Dll/UserAgent.dll
/reference:Assets/Dll/UTools.dll
/reference:Assets/Dll/Editor/FTPHelperEditor.dll
/reference:Assets/Dll/Editor/HowinEditor.dll
/reference:Assets/Dll/Editor/U3DPackerEditor.dll
/reference:Assets/Dll/Editor/UTestHelper.dll
/reference:Assets/OwnTools/KV/AttributeTool/AttributeTool.dll
/reference:Assets/OwnTools/KV/AttributeTool/Editor/AttributeToolEditor.dll
/reference:Assets/Plugins/Analytics.dll
/reference:Assets/Plugins/AnalyticsUnityLibrariesAndroid.dll
/reference:Assets/Plugins/ArabicSupport.dll
/reference:Assets/Plugins/Backend.dll
/reference:Assets/Plugins/BackendSerializer.dll
/reference:Assets/Plugins/BillingUnityLibrariesAndroid.dll
/reference:Assets/Plugins/DOTween.dll
/reference:Assets/Plugins/DOTween43.dll
/reference:Assets/Plugins/DOTween46.dll
/reference:Assets/Plugins/DOTween50.dll
/reference:Assets/Plugins/Facebook.Unity.Settings.dll
/reference:Assets/Plugins/LevelDataMeta.dll
/reference:Assets/Plugins/LevelDataMetaSerializer.dll
/reference:Assets/Plugins/protobuf-net.dll
/reference:Assets/Plugins/Validator.dll
/reference:E:/project/DogBlast_new/Library/PackageCache/com.unity.collab-proxy@1.14.18/Lib/Editor/PlasticSCM/log4netPlastic.dll
/reference:E:/project/DogBlast_new/Library/PackageCache/com.unity.collab-proxy@1.14.18/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll
/reference:E:/project/DogBlast_new/Library/PackageCache/com.unity.collab-proxy@1.14.18/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:E:/project/DogBlast_new/Library/PackageCache/com.unity.collab-proxy@1.14.18/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:Assets/Dll/LGPOfflineSDK.dll
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/ref/2.0.0/netstandard.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.AppContext.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Collections.Concurrent.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Collections.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Collections.NonGeneric.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Collections.Specialized.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ComponentModel.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ComponentModel.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Console.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Data.Common.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Contracts.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Debug.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Process.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Tools.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Diagnostics.Tracing.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Drawing.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Dynamic.Runtime.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Globalization.Calendars.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Globalization.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Globalization.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.Compression.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.FileSystem.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.IsolatedStorage.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.Pipes.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Linq.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Linq.Expressions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Linq.Parallel.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Linq.Queryable.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Http.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.NameResolution.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.NetworkInformation.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Ping.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Requests.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Security.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.Sockets.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.WebSockets.Client.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Net.WebSockets.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ObjectModel.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Reflection.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Reflection.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Reflection.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Resources.Reader.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Resources.ResourceManager.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Resources.Writer.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Handles.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.InteropServices.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Numerics.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Claims.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.Principal.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Security.SecureString.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Text.Encoding.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Text.RegularExpressions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Overlapped.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Tasks.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Thread.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.ThreadPool.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Threading.Timer.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.ValueTuple.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.ReaderWriter.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XDocument.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XmlDocument.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XmlSerializer.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XPath.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Numerics.Vectors.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/mscorlib.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.ComponentModel.Composition.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Core.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Data.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Drawing.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.IO.Compression.FileSystem.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Net.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Numerics.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Runtime.Serialization.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.ServiceModel.Web.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Transactions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Web.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Windows.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Xml.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Xml.Linq.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/NetStandard/compat/2.0.0/shims/netfx/System.Xml.Serialization.dll"
/define:UNITY_2019_4_40
/define:UNITY_2019_4
/define:UNITY_2019
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:PLATFORM_ARCH_64
/define:UNITY_64
/define:UNITY_INCLUDE_TESTS
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_MICROPHONE
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_PHYSICS
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_UNET
/define:ENABLE_LZMA
/define:ENABLE_UNITYEVENTS
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_WWW
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:INCLUDE_DYNAMIC_GI
/define:ENABLE_MONO_BDWGC
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:PLATFORM_SUPPORTS_MONO
/define:RENDER_SOFTWARE_CURSOR
/define:ENABLE_VIDEO
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:UNITY_STANDALONE_WIN
/define:UNITY_STANDALONE
/define:ENABLE_RUNTIME_GI
/define:ENABLE_MOVIES
/define:ENABLE_NETWORK
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CLUSTERINPUT
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_MONO
/define:NET_STANDARD_2_0
/define:ENABLE_PROFILER
/define:DEBUG
/define:TRACE
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_BURST_AOT
/define:UNITY_TEAM_LICENSE
/define:UNITY_PRO_LICENSE
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_LOCALIZATION
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:CSHARP_7_OR_LATER
/define:CSHARP_7_3_OR_NEWER
E:\project\DogBlast_new\Library\PackageCache\com.unity.xr.legacyinputhelpers@2.1.9\Runtime\ArmModels\ArmModel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.xr.legacyinputhelpers@2.1.9\Runtime\ArmModels\SwingArmModel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.xr.legacyinputhelpers@2.1.9\Runtime\ArmModels\TransitionArmModel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.xr.legacyinputhelpers@2.1.9\Runtime\CameraOffset.cs
